var sceneReadyBehavior = require('../behavior-scene/scene-ready');
var yuvBufferToImage = require('./utils/yuvBufferToImage')
var FormData = require('../../utils/form-data/formData')
var wasmLoader = require('../../utils/wasmLoader')
var markerTransform = require('./utils/markerTransform')
var vpsService = require('../../services/vpsService');
const { AnchorData, AnchorPose } = require('../../data/models');
const { roomsData } = require('./configs/rooms-data');
let trackingMarker = null
let lastVpsRoamingSuccess = false
let vpsRetryTimeoutId = null
const distanceThreshold = 1.8
const backToMarkerThreshold = 90
const facingMarkerThreshold = 45

// 防止频繁弹窗的机制
let lastModalTime = {}  // 记录每个marker上次弹窗时间
let userRejectedMarkers = {}  // 记录用户拒绝进入的marker和时间
let markerVisitStatus = {}  // 记录展项访问状态 {markerId: 'unvisited'|'visited'|'visiting'}
let roomVisitStatus = {} // 记录展区访问状态 {roomId: 'unvisited'|'visited'}
let isModalShowing = false  // 标记是否有弹窗正在显示
const MODAL_COOLDOWN = 10000  // 弹窗冷却时间10秒
const REJECTION_COOLDOWN = 30000  // 用户拒绝后的冷却时间30秒

let isChatLoading = false

const xr = wx.getXrFrameSystem();

Page({
  behaviors:[sceneReadyBehavior],
  data: {
    anchorList: [],
    initialVpsTracked: false,
    showBackBtn: true,
    isProximityCheckOn: false,
    isVisitingItem: false,
    markerLeft: 50,
    markerTop: 50,
    markerWidth: 0,
    markerHeight: 0,
    unfinishedItemsCount: 0,
    checkDistanceIntervalId: null,
    transformMatrix: null,
    arTracked: false,
    // debug only
    cameraPoseText: '',
    originalPoseText: ''
  },
  arRawData: null,
  userPose: null,

  origUserPose: null,
  markerLockComponent: null,
  currentRoom: null,
  targetRoom: null,
  onLoad() {
    if (!this.data.arTracked) {
      wx.showLoading({
        title: 'AR初始化',
      })
    }
    this.currentRoom = roomsData[0]
  },
  onShow() {
    // 确保页面重新显示时状态正确初始化
    trackingMarker = null
    lastVpsRoamingSuccess = false
    console.log('onShow: 重置全局跟踪状态')
  },
  onUnload() {
    if (vpsRetryTimeoutId) {
      clearTimeout(vpsRetryTimeoutId)
      vpsRetryTimeoutId = null
    }
    this.stopTracking()
    // 清理防频繁弹窗的记录
    lastModalTime = {}
    userRejectedMarkers = {}
    markerVisitStatus = {}
    roomVisitStatus = {}
    isModalShowing = false
    // 清理全局跟踪状态
    trackingMarker = null
    isChatLoading = false
  },

  // 清理过期的记录（可选，定期调用以避免内存泄漏）
  cleanupExpiredRecords() {
    const now = Date.now();

    // 清理过期的弹窗记录
    Object.keys(lastModalTime).forEach(markerId => {
      if (now - lastModalTime[markerId] > MODAL_COOLDOWN * 2) {
        delete lastModalTime[markerId];
      }
    });

    // 清理过期的拒绝记录
    Object.keys(userRejectedMarkers).forEach(markerId => {
      if (now - userRejectedMarkers[markerId] > REJECTION_COOLDOWN * 2) {
        delete userRejectedMarkers[markerId];
      }
    });
  },
  calculateTransformationMatrices(newMarkerPose) {
    // 从本地缓存加载建图时存储的marker在AR坐标系下的位姿
    const markerRecordedSlamPose = wx.getStorageSync(newMarkerPose.id);
    console.log("初始SLAM pose为: position: " + JSON.stringify(markerRecordedSlamPose.position) + ", rotation: " + JSON.stringify(markerRecordedSlamPose.rotation))
    console.log("新SLAM pose为：position: " + JSON.stringify(newMarkerPose.position) + ", rotation: " + JSON.stringify(newMarkerPose.rotation))
    const {R, T} = markerTransform.calculateTransformationMatrix(markerRecordedSlamPose, newMarkerPose)
    this.setData({
      rotationTransform: R,
      translationTransform: T
    })
  },
  // 申请初始定位
  async requestVpsEvent(evt) {
    if (!this.data.arTracked) {
      return
    }
    const { projectId } = evt.currentTarget.dataset;
    console.log('vps projectId: '+projectId)
    // console.log('----请求定位时，相机的原始位姿是-----: x: '+this.origUserPose.position.x+', y: '+this.origUserPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+euler.x+', y: '+euler.y+', z: '+euler.z)
    //此处使用AR系统纠偏后的相机位姿，是因为该位姿已经经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
    let queryPose = {
      position: xr.Vector3.createFromNumber(this.userPose.position.x, this.userPose.position.y, this.userPose.position.z),
      quaternion: this.userPose.quaternion
    }

    await this.saveCurrentFrame(projectId, queryPose)
  },
  async requestVps(projectId) {
    if (!this.data.arTracked) {
      return
    }
    // console.log('----请求定位时，相机的原始位姿是-----: x: '+this.origUserPose.position.x+', y: '+this.origUserPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+euler.x+', y: '+euler.y+', z: '+euler.z)
    //此处使用AR系统纠偏后的相机位姿，是因为该位姿已经经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
    let queryPose = {
      position: xr.Vector3.createFromNumber(this.userPose.position.x, this.userPose.position.y, this.userPose.position.z),
      quaternion: this.userPose.quaternion
    }

    await this.saveCurrentFrame(projectId, queryPose)
  },
  async saveCurrentFrame(projectId, queryPose) {
    try {
      if (!this.arRawData) {
        console.log('arRawData is empty')
        return
      }
      wx.showLoading({
        title: '定位中',
      })
      const {yBuffer, uvBuffer, width, height, intrinsics} = this.arRawData
      const imageBuffer = await yuvBufferToImage.yuvToImage(yBuffer, uvBuffer, width, height)
      const vpsIntrinsics = [intrinsics[0],intrinsics[4],intrinsics[6],intrinsics[7],width,height]
      console.log('intrinsics raw: '+intrinsics)
      console.log('intrinsics: '+vpsIntrinsics)
      const formData = new FormData()
      formData.appendFile('file', imageBuffer, 'pic.jpg')
      formData.append('latitude', '39.992194')
      formData.append('longitude', '116.329943')
      formData.append('type', 2)
      formData.append('projectId', projectId)
      formData.append('userId', '6')

      formData.append('intrinsics', vpsIntrinsics)

      const euler = queryPose.quaternion.toEulerAngles()
      console.log('queryPose: position: x: '+queryPose.position.x+', y: '+queryPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+euler.x+', y: '+euler.y+', z: '+euler.z)

      const vpsInfo = await vpsService.vpsRoam(formData)
      const vpsPosition = {x: vpsInfo.tcw[0], y: vpsInfo.tcw[1], z: vpsInfo.tcw[2]}
      const vpsQuaternion = xr.Quaternion.createFromNumber(vpsInfo.qcw[1], vpsInfo.qcw[2], vpsInfo.qcw[3], vpsInfo.qcw[0])
      const vpsEuler = vpsQuaternion.toEulerAngles()
      const vpsPose = {
        position: xr.Vector3.createFromNumber(vpsPosition.x, vpsPosition.y, vpsPosition.z),
        quaternion: vpsQuaternion
      }
      const cameraEuler = queryPose.quaternion.toEulerAngles()
      console.log('--------queryPose的值是------: position: x: '+queryPose.position.x+', y: '+queryPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+markerTransform.radianToAngle(cameraEuler.x)+', y: '+markerTransform.radianToAngle(cameraEuler.y)+', z: '+markerTransform.radianToAngle(cameraEuler.z))

      console.log(" --------vpsPose的值是------:"+JSON.stringify(vpsPose.position) +","+"vpsPose rotation:"+ JSON.stringify(vpsPose.quaternion.toEulerAngles()))

      const transformMatrix = markerTransform.calculateTransformationMatrix(queryPose, vpsPose)
      this.setData({
        transformMatrix: transformMatrix
      })
      lastVpsRoamingSuccess = true
      if (vpsRetryTimeoutId) {
        clearTimeout(vpsRetryTimeoutId)
        vpsRetryTimeoutId = null
      }
      wx.hideLoading()
      wx.showToast({
        title: '定位成功',
        icon: 'success'
      })
        const vpsAnchorList = vpsInfo.deltaPositionList
        const anchorList = []

        vpsAnchorList.forEach(anchor => {
          const anchorPositionArray = JSON.parse(anchor.position)
          const anchorRotationArray = JSON.parse(anchor.rotation)
          const anchorScaleArray = JSON.parse(anchor.scale)
          //从云测返回的anchor位姿
          const vpsAnchorPose = {
            position: xr.Vector3.createFromNumber(anchorPositionArray[0], anchorPositionArray[1], anchorPositionArray[2]),
            quaternion: xr.Quaternion.createFromNumber(anchorRotationArray[1],anchorRotationArray[2],anchorRotationArray[3],anchorRotationArray[0])
          }
          console.log("--------Anchor的VPSPose是------："+ JSON.stringify(vpsAnchorPose))
          const scale = {x: anchorScaleArray[0], y: anchorScaleArray[1], z: anchorScaleArray[2]}

          let vpsAnchorMatrix = xr.Matrix4.composeTQS(vpsAnchorPose.position, vpsAnchorPose.quaternion, xr.Vector3.ONE)

          let Tmatrix = new xr.Matrix4()
           Tmatrix.setArray(transformMatrix)
           console.log("--------Anchor的转换矩阵是------："+ JSON.stringify(markerTransform.matrixToPose(Tmatrix)))
          let slam_anchor_matrix = Tmatrix.multiply(vpsAnchorMatrix)

          let convertedMatrix = markerTransform.matrixLeftToRightMatrixY(slam_anchor_matrix)

          let convertedPose = markerTransform.matrixToPose(convertedMatrix)

          const convertedEuler = convertedPose.quaternion.toEulerAngles()
          const transformedPose2 = {position: {x: convertedPose.position.x, y: convertedPose.position.y, z: convertedPose.position.z}, rotation: {x: convertedEuler.x, y: convertedEuler.y, z: convertedEuler.z}}
          console.log('transformedPose2: '+JSON.stringify(transformedPose2))
          //const anchorUrl = this.tryGetFirstGlb(anchor.models)
          const anchorData = new AnchorData(anchor.anchorId, transformedPose2.position, transformedPose2.rotation, scale, '', anchor.assetId)

          anchorList.push(anchorData)
        });
        if (!this.data.initialVpsTracked) {
          this.setData({
            initialVpsTracked: true,
            anchorList: anchorList
          })
          await this.handleOrderRequest('entrance')
        } else {
          this.setData({
            anchorList: anchorList
          })
        }
        // 重新定位后刷新anchor显示状态
        // this.refreshAnchorList()
        // await this.handleOrderRequest('introduce')
    } catch(err) {
      console.log(err+', retry saveCurrentFrame')
      wx.hideLoading()
      wx.showToast({
        title: '定位失败',
        icon: 'error'
      })
      lastVpsRoamingSuccess = false
      // 确保仅设置一个重试计时器
      // if (!vpsRetryTimeoutId) {
      //   vpsRetryTimeoutId = setTimeout(async () => {
      //     vpsRetryTimeoutId = null;  // 重置定时器标志
      //     await this.saveCurrentFrame(queryPose)
      //   }, 5000)
      // }
    }
  },
  tryGetFirstGlb(modelUrls) {
    if (!modelUrls || !Array.isArray(modelUrls) || modelUrls.length === 0) {
      console.log('modelUrls是空或未定义')
      return null;
    }
    const firstGlb = modelUrls.find(url => url.toLowerCase().endsWith('.glb') || url.toLowerCase().endsWith('.gltf'));
    return firstGlb;
  },
  checkDistance(cameraPos, cameraQuat) {
    // 如果有弹窗正在显示，跳过检测
    if (isModalShowing) {
      console.log('弹窗正在显示，跳过距离检测')
      return
    }

    const anchorList = this.data.anchorList
    // console.log('anchorList length: '+anchorList.length)
    anchorList.forEach(async (marker, idx) => {
      if (!marker.position) {
        console.log('marker position is undefined')
        return
      }
      const distance = this.calculateDistance(marker.position, cameraPos);
      // console.log('cameraPos: '+JSON.stringify(cameraPos)+', markerPos: '+JSON.stringify(marker.position)+'距离'+marker.id+': '+distance)

      if (trackingMarker && trackingMarker.id === marker.id) {
        // 改进的离开判断：先检查朝向，再检查距离
        const isLeavingMarker = this.isLeavingMarker(marker.position, cameraPos, cameraQuat, distance);

        if (isLeavingMarker) {
            await this.handleItemExit(marker)
            this.setData({
              [`anchorList[${idx}].isActive`]: false
            })
            // 标记为已访问
            markerVisitStatus[marker.id] = 'visited'
            this.refreshAnchorList()
            trackingMarker = null
            return
          // this.stopTracking()
          // isModalShowing = true
          // try {
          //   wx.showModal({
          //     title: '您已离开'+marker.name+'展区',
          //     content: '是否确认结束体验？',
          //     confirmText: '确认离开', // 自定义确认按钮的文字
          //     cancelText: '暂不离开', // 自定义取消按钮的文字
          //     success: (res) => {
          //       isModalShowing = false
          //       if (res.confirm) {
          //         this.handleItemExit(marker)
          //         this.setData({
          //           [`anchorList[${idx}].isActive`]: false
          //         })
          //         // 标记为已访问
          //         markerVisitStatus[marker.id] = 'visited'
          //         this.refreshAnchorList()
          //         trackingMarker = null
          //         this.resumeTracking()
          //         return
          //       } else if (res.cancel) {
          //         this.resumeTracking()
          //       }
          //     }
          //   })
          // } catch(err) {
          //   console.log('on exit current item: '+err)
          // }
        }
      }

      else if (!trackingMarker) {
        // 使用新的综合判断方法（需要确保userPose存在）
        let isReady = false;
        if (!cameraQuat) {
          console.log('cameraQuat is null')
          // 如果没有相机朝向信息，回退到仅使用距离判断
          isReady = distance < distanceThreshold;
        } else {
          const isFacingMarker = this.isReadyToEnterMarker(marker.position, cameraPos, cameraQuat, distance);
          // console.log(`isFacingMarker ${marker.id}: `+isFacingMarker)
          isReady = distance < distanceThreshold && isFacingMarker
        }

        if (isReady) {
          if (this.currentRoom.nextRoom && marker.id === this.currentRoom.nextRoom.exitAnchorId) {
            console.log('识别到在下一展厅门口')
            this.stopTracking()
            this.targetRoom = roomsData.find(room => room.id === this.currentRoom.nextRoom.id)
            this.handleRoomTransition(this.targetRoom)
          }
          else if (this.currentRoom.previousRoom && marker.id === this.currentRoom.previousRoom.exitAnchorId) {
            console.log('识别到在下一展厅门口')
            this.stopTracking()
            this.targetRoom = roomsData.find(room => room.id === this.currentRoom.previousRoom.id)
            this.handleRoomTransition(this.targetRoom)
          }
          else if (this.currentRoom.exitAnchorId && marker.id === this.currentRoom.exitAnchorId) {
              console.log('识别到在出口范围内')
              this.stopTracking()
              this.handleLeaveIntent()
          }
          else {
            // 检查展项状态和是否应该显示弹窗
            const visitStatus = markerVisitStatus[marker.id] || 'unvisited'
            // TODO: 暂时移除visitStatus !== 'visited'检测
            if (this.shouldShowEnterModal(marker.id)) {
                // 用户确认进入，清除拒绝记录，标记为正在访问
                wx.showToast({
                  title: '您已进入'+marker.name
                })
                delete userRejectedMarkers[marker.id]
                markerVisitStatus[marker.id] = 'visiting'
                await this.handleItemEnter(marker)
                this.setData({
                  [`anchorList[${idx}].isActive`]: true
                })
                this.refreshAnchorList()
                trackingMarker = marker
              // this.stopTracking()
              // isModalShowing = true
              // wx.showModal({
              //   title: '您已进入'+marker.name+'展区',
              //   content: '是否确认开启体验？',
              //   confirmText: '开启体验', // 自定义确认按钮的文字
              //   cancelText: '稍后再说', // 自定义取消按钮的文字
              //   success: (res) => {
              //     isModalShowing = false
              //     if (res.confirm) {
              //       // 用户确认进入，清除拒绝记录，标记为正在访问
              //       delete userRejectedMarkers[marker.id]
              //       markerVisitStatus[marker.id] = 'visiting'
              //       this.handleItemEnter(marker)
              //       this.setData({
              //         [`anchorList[${idx}].isActive`]: true
              //       })
              //       this.refreshAnchorList()
              //       trackingMarker = marker
              //       this.resumeTracking()
              //       return
              //     } else if (res.cancel) {
              //       // 用户拒绝进入，记录拒绝时间
              //       userRejectedMarkers[marker.id] = Date.now()
              //       this.resumeTracking()
              //     }
              //   }
              // })
              // // 记录弹窗时间
              // lastModalTime[marker.id] = Date.now()
            }
            // wx.showToast({
            //   title: '距离'+marker.id+': '+distance,
            // })
          }
        }
      }
    });
  },
  spawnCameraMesh() {
    if (!this.markerLockComponent) {
      this.markerLockComponent = this.selectComponent('#main-frame')
    }
    if (this.markerLockComponent) {
      this.markerLockComponent.spawnCameraPoseMesh()
    }
  },
  refreshAnchorList() {
    if (!this.markerLockComponent) {
      this.markerLockComponent = this.selectComponent('#main-frame')
    }
    if (this.markerLockComponent) {
      // this.data.anchorList.forEach(anchor => {
      //   console.log('anchor' + anchor.id + ' position: '+JSON.stringify(anchor.position)+', rotation: x: '+(anchor.rotation.x)+', y: '+(anchor.rotation.y)+', z: '+(anchor.rotation.z))
      //   this.markerLockComponent.spawnAnchorItem(anchor)
      // })
      this.markerLockComponent.refreshActiveAnchorList(this.data.anchorList)
    } else {
      console.log('Cannot find markerLock component in the page')
    }
  },
  // 计算三维空间距离的方法
  calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dz * dz);
  },

  // 判断相机是否准备进入marker（综合考虑距离和朝向）
  // 参数说明：
  // markerPos: marker的位置 {x, y, z}
  // cameraPos: 相机的位置 {x, y, z}
  // cameraQuat: 相机的四元数旋转
  // 返回值: boolean - true表示可以进入marker
  isReadyToEnterMarker(markerPos, cameraPos, cameraQuat, distance) {
    // 1. 计算相机到marker的方向（xz平面）
    const toMarkerX = markerPos.x - cameraPos.x;
    const toMarkerZ = markerPos.z - cameraPos.z;
    const toMarkerLength = Math.sqrt(toMarkerX * toMarkerX + toMarkerZ * toMarkerZ);

    if (toMarkerLength < 0.1) {
      return true; // 距离太近直接进入
    }

    const toMarkerDirX = toMarkerX / toMarkerLength;
    const toMarkerDirZ = toMarkerZ / toMarkerLength;

    // 2. 获取相机前向向量（xz平面）
    const forward = this.quatToForwardXZ(cameraQuat);

    // 3. 点积判断夹角
    const dotProduct = toMarkerDirX * forward.x + toMarkerDirZ * forward.z;
    const angleThreshold = Math.cos(Math.PI / 4); // 45°

    // 调试信息
    const actualAngle = Math.acos(Math.max(-1, Math.min(1, dotProduct))) * 180 / Math.PI;
    console.log(`相机朝向检测 - 到marker角度: ${actualAngle.toFixed(1)}°, 阈值: 45°, 距离: ${distance.toFixed(2)}m`);

    return dotProduct > angleThreshold;
  },
  // 将四元数转为XZ平面上的前向向量
  quatToForwardXZ(q) {
    const x = q.x, y = q.y, z = q.z, w = q.w;

    // forward = q * (0,0,-1) * q^-1
    const forwardX = 2 * (x * z + w * y);
    const forwardY = 2 * (y * z - w * x);
    const forwardZ = 1 - 2 * (x * x + y * y);

    // 投影到XZ平面并归一化
    const length = Math.sqrt(forwardX * forwardX + forwardZ * forwardZ);
    return {
      x: forwardX / length,
      z: forwardZ / length
    };
  },

  // 判断用户是否正在离开marker（优先考虑朝向，再考虑距离）
  isLeavingMarker(markerPos, cameraPos, cameraQuat, distance) {
    // 1. 如果没有朝向信息，仅使用距离判断
    if (!cameraQuat) {
      console.log('离开检测 - 无朝向信息，使用距离判断:', distance >= distanceThreshold);
      return distance >= distanceThreshold;
    }

    // 2. 计算相机到marker的方向
    const toMarkerX = markerPos.x - cameraPos.x;
    const toMarkerZ = markerPos.z - cameraPos.z;
    const toMarkerLength = Math.sqrt(toMarkerX * toMarkerX + toMarkerZ * toMarkerZ);

    if (toMarkerLength < 0.1) {
      return false; // 距离太近，不算离开
    }

    const toMarkerDirX = toMarkerX / toMarkerLength;
    const toMarkerDirZ = toMarkerZ / toMarkerLength;

    // 3. 获取相机前向向量
    const forward = this.quatToForwardXZ(cameraQuat);

    // 4. 计算相机朝向与marker方向的夹角
    const dotProduct = toMarkerDirX * forward.x + toMarkerDirZ * forward.z;
    const actualAngle = Math.acos(Math.max(-1, Math.min(1, dotProduct))) * 180 / Math.PI;

    // 5. 优先判断朝向：如果用户背对marker（角度大于120度），即使距离较近也认为要离开
    const isBackToMarker = actualAngle > backToMarkerThreshold;

    // 6. 综合判断：背对marker 或者 (距离远 且 不是面向marker)
    const isFacingMarker = actualAngle < facingMarkerThreshold; // 面向marker的角度阈值
    const isLeavingByDistance = distance >= distanceThreshold && !isFacingMarker;
    const isLeaving = isBackToMarker || isLeavingByDistance;

    console.log(`离开检测 - 角度: ${actualAngle.toFixed(1)}°, 距离: ${distance.toFixed(2)}m, 背对: ${isBackToMarker}, 面向: ${isFacingMarker}, 离开: ${isLeaving}`);

    return isLeaving;
  },

  // 检查是否应该显示进入弹窗（防止频繁弹窗）
  shouldShowEnterModal(markerId) {
    const now = Date.now();

    // 检查是否在冷却期内
    const lastModal = lastModalTime[markerId];
    if (lastModal && (now - lastModal) < MODAL_COOLDOWN) {
      console.log(`marker ${markerId} 在弹窗冷却期内，跳过`);
      return false;
    }

    // 检查用户是否最近拒绝过
    const rejectedTime = userRejectedMarkers[markerId];
    if (rejectedTime && (now - rejectedTime) < REJECTION_COOLDOWN) {
      console.log(`marker ${markerId} 在用户拒绝冷却期内，跳过`);
      return false;
    }

    return true;
  },

  // 重置展项访问状态（可用于测试或特殊情况）
  resetMarkerVisitStatus(markerId = null) {
    if (markerId) {
      // 重置特定展项
      delete markerVisitStatus[markerId];
      delete userRejectedMarkers[markerId];
      delete lastModalTime[markerId];
      console.log(`已重置展项 ${markerId} 的访问状态`);
    } else {
      // 重置所有展项
      markerVisitStatus = {};
      userRejectedMarkers = {};
      lastModalTime = {};
      console.log('已重置所有展项的访问状态');
    }
  },

  // 获取展项访问状态
  getMarkerVisitStatus(markerId) {
    return markerVisitStatus[markerId] || 'unvisited';
  },

  // 获取所有展项访问状态
  getAllMarkerVisitStatus() {
    return { ...markerVisitStatus };
  },
  showLeaveModal() {
    const unfinishedItemsCount = this.data.unfinishedItemsCount
    isModalShowing = true
    wx.showModal({
      title: unfinishedItemsCount > 0 ? `您还有${unfinishedItemsCount}个展项没有看呢！` : '您已到达出口附近，已参观完全部展项。',
      content: unfinishedItemsCount > 0 ? '真的要走吗？' : '是否结束参观？',
      confirmText: unfinishedItemsCount > 0 ? '再看看' : '结束参观', // 自定义确认按钮的文字
      cancelText: unfinishedItemsCount > 0 ? '结束参观' : '再看看', // 自定义取消按钮的文字
      success: (res) => {
        isModalShowing = false
        if (res.confirm) {
          console.log('用户点击确认')
          if (unfinishedItemsCount <= 0) {
            this.triggerEvent('endSession')
          } else {
            this.resumeTracking()
          }
          return
        } else if (res.cancel) {
          console.log('用户点击取消')
          if (unfinishedItemsCount > 0) {
            this.triggerEvent('endSession')
          } else {
            this.resumeTracking()
          }
        }
      }
    })
  },
  showTransitionModal(targetRoom) {
    if (this.shouldShowEnterModal(this.currentRoom.exitAnchorId)) {
      isModalShowing = true
      wx.showModal({
        title: roomVisitStatus[targetRoom.id] && roomVisitStatus[targetRoom.id] === 'visited' ? `您已参观过${targetRoom.id}号展区，是否重新进入？` : `您已到达${targetRoom.id}号展区门口，是否开始参观？`,
        content: '如确定开始参观，请在确认后持稳手机，系统将重新定位',
        confirmText: '确认参观',
        cancelText: '再想想',
        success: async (res) => {
          if (res.confirm) {
            //console.log('用户点击确认')
            // 重新进入房间时，重置访问状态以允许重新访问，但保存当前的isActive状态
            this.data.anchorList.forEach((anchor) => {
              this.resetMarkerVisitStatus(anchor.id)
            })
            roomVisitStatus[this.currentRoom.id] = 'visited'
            await this.requestVps(targetRoom.projectId)
            this.currentRoom = targetRoom
            await this.handleOrderRequest('entrance', targetRoom.id, targetRoom.name)
          } else if (res.cancel) {
            //console.log('用户点击取消')
            userRejectedMarkers[this.currentRoom.exitAnchorId] = Date.now()
            this.resumeTracking()
          }
          isModalShowing = false
        }
      })
      lastModalTime[this.currentRoom.exitAnchorId] = Date.now()
    } else {
      // 如果不应该显示弹窗，直接恢复跟踪
      this.resumeTracking()
    }
  },
  stopTracking() {
    if (!this.data.isProximityCheckOn) {
      console.log('stopTracking: not tracking, returning')
      return
    }
    clearInterval(this.data.checkDistanceIntervalId)
    this.setData({
      isProximityCheckOn: false,
      checkDistanceIntervalId: null
    })
  },
  resumeTracking() {
    if (this.data.isProximityCheckOn || !this.data.initialVpsTracked) return
    const checkDistanceIntervalId = setInterval(() => {
      this.checkDistance(this.userPose.position, this.userPose.quaternion)
      // 每次检查时也清理过期记录
      this.cleanupExpiredRecords()
    }, 3000)
    this.setData({
      checkDistanceIntervalId: checkDistanceIntervalId,
      isProximityCheckOn: true
    })
  },
  // 初始AR初始化成功
  handleArInit(evt) {
    this.setData({
      arTracked: true
    })
    wx.hideLoading()
  },
  handleArLost(evt) {
    this.setData({
      arTracked: false
    })
    wx.showLoading({
      title: 'AR初始化',
    })
  },
  // SLAM丢失后，AR再次初始化成功
  handleArTracked(evt) {
    this.setData({
      arTracked: true
    })
    wx.hideLoading()
  },
  // 用于记录原始相机位姿
  handleOriginalCameraPoseTick(evt) {
    const {cameraPos, cameraQuat} = evt.detail
    const cameraEuler = cameraQuat.toEulerAngles()
    this.setData({
      originalPoseText: "originalSlamPose:x:"+cameraPos.x.toFixed(3)+", y:"+cameraPos.y.toFixed(3)+', z:'+cameraPos.z.toFixed(3)+', 旋转:x:'+markerTransform.radianToAngle(cameraEuler.x).toFixed(3)+', y:'+markerTransform.radianToAngle(cameraEuler.y).toFixed(3)+', z:'+markerTransform.radianToAngle(cameraEuler.z).toFixed(3),
    })
    //原始相机位姿
    this.origUserPose = {
      position: {x: cameraPos.x, y: cameraPos.y, z: cameraPos.z},
      quaternion: cameraQuat
    }
  },
  // 用于记录VPS纠偏后的相机位姿
  handleCameraPoseTick(evt) {
    const {cameraPos, cameraQuat, arRawData} = evt.detail
    const cameraEuler = cameraQuat.toEulerAngles()
    this.setData({
      cameraPoseText: "vpsCorrectedPose:x:"+cameraPos.x.toFixed(3)+", y:"+cameraPos.y.toFixed(3)+', z:'+cameraPos.z.toFixed(3)+', 旋转:x:'+markerTransform.radianToAngle(cameraEuler.x).toFixed(3)+', y:'+markerTransform.radianToAngle(cameraEuler.y).toFixed(3)+', z:'+markerTransform.radianToAngle(cameraEuler.z).toFixed(3),
    })
    if (!this.arRawData) {
      this.arRawData = arRawData
    }
    this.userPose = {
      position: {x: cameraPos.x, y: cameraPos.y, z: cameraPos.z},
      quaternion: cameraQuat
    }

    // 用于实时检测anchor与摄像机之间的距离
    if (!this.data.isProximityCheckOn) {
      this.resumeTracking()
    }
  },
  async handleItemEnter(markerInfo) {
    this.setData({
      isVisitingItem: true,
      markerWidth: Math.floor(this.data.width * 0.5),
      markerHeight: Math.floor(this.data.width * 0.5)
    })
    // 清除该marker的拒绝记录
    delete userRejectedMarkers[markerInfo.id]
    // send order request for entering item
    await this.handleOrderRequest('start', markerInfo.exhibitItemId, markerInfo.name)
  },
  async handleItemExit(markerInfo) {
    this.setData({
      isVisitingItem: false
    })
    // send order request for exiting item
    await this.handleOrderRequest('stop', markerInfo.exhibitItemId, markerInfo.name)
  },
  handleEndSession(evt) {
    this.handleOrderRequest('exit')
  },
  async handleLeaveIntent(evt = undefined) {
    await this.getQueryItems()
    this.showLeaveModal()
  },
  async handleRoomTransition(targetRoom) {
    console.log('targetRoom: '+JSON.stringify(targetRoom))
    this.showTransitionModal(targetRoom)
  },
  async handleTrackerPositionReceived(evt) {
    const item = evt.detail
    const markerPose = {
      id: item.id,
      position: item.position,
      rotation: item.rotation
    }
    this.calculateTransformationMatrices(markerPose)
    const anchorList = this.data.anchorList
    anchorList.forEach(marker => {
      if (marker.id === 'entryMarker') {
        marker.pos = markerPose.position
      }
      else {
        const translationTransform = this.data.translationTransform
        const rotationTransform = this.data.rotationTransform
        const markerPos1 = wx.getStorageSync(marker.id)
        if (markerPos1) {
          const transformedPose = markerTransform.getTransformedPose(translationTransform, markerPos1)
          marker.pos = transformedPose.position
        }
      }
    });
    this.setData({
      anchorList: anchorList,
      initialVpsTracked: true
    })
    await this.handleOrderRequest('entrance')
    await this.handleOrderRequest('introduce')
  },
  async handleOrderRequest(instruction, project_id, project_name) {
    const agentChatViewerComponent = this.selectComponent('#agent-chat-viewer')
    if (agentChatViewerComponent) {
      await agentChatViewerComponent.sendOrderMessage(instruction, project_id, project_name)
    } else {
      console.log('Cannot find agentChatViewerComponent.')
    }
  },
  async getQueryItems() {
    const agentChatViewerComponent = this.selectComponent('#agent-chat-viewer')
    if (agentChatViewerComponent) {
      const queryItems = await agentChatViewerComponent.getQueryItems()
      const unfinishedItemsCount = queryItems.filter(item => item.state === 0).length
      console.log('还有'+unfinishedItemsCount+'项没有看完。')
      this.setData({
        unfinishedItemsCount: unfinishedItemsCount
      })
    } else {
      console.log('Cannot find agentChatViewerComponent.')
    }
  },
});